<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Battle UI Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e40af 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        /* 手機直向戰鬥佈局 */
        .battle-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 頂部控制欄 */
        .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stage-info {
            font-size: 16px;
            font-weight: bold;
        }

        .header-controls {
            display: flex;
            gap: 12px;
        }

        .control-btn {
            width: 36px;
            height: 36px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        /* 敵方區域 (30%) */
        .enemy-section {
            background: rgba(239,68,68,0.1);
            padding: 15px;
            border-bottom: 2px solid rgba(239,68,68,0.3);
            min-height: 30vh;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: bold;
        }

        .enemy-header {
            color: #fca5a5;
        }

        .cards-grid {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .battle-card {
            width: 80px;
            height: 100px;
            background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            border-radius: 8px;
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            padding: 8px 4px;
            font-size: 12px;
        }

        .battle-card.enemy {
            border-color: rgba(239,68,68,0.5);
            background: linear-gradient(145deg, rgba(239,68,68,0.2), rgba(239,68,68,0.1));
        }

        .battle-card.player {
            border-color: rgba(59,130,246,0.5);
            background: linear-gradient(145deg, rgba(59,130,246,0.2), rgba(59,130,246,0.1));
        }

        .battle-card:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .card-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .card-stats {
            display: flex;
            gap: 4px;
            font-size: 10px;
            font-weight: bold;
        }

        .hp-bar {
            width: 100%;
            height: 4px;
            background: rgba(0,0,0,0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 4px;
        }

        .hp-fill {
            height: 100%;
            background: linear-gradient(90deg, #ef4444, #dc2626);
            transition: width 0.3s ease;
        }

        .hp-fill.player {
            background: linear-gradient(90deg, #10b981, #059669);
        }

        /* 中間行動區域 */
        .action-section {
            background: rgba(0,0,0,0.2);
            padding: 10px 20px;
            backdrop-filter: blur(5px);
        }

        .next-action {
            font-size: 14px;
            margin-bottom: 8px;
            text-align: center;
        }

        .action-queue {
            display: flex;
            justify-content: center;
            gap: 8px;
            align-items: center;
        }

        .action-indicator {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }

        .action-indicator.player {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }

        .action-indicator.enemy {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        /* 玩家區域 (40%) */
        .player-section {
            background: rgba(59,130,246,0.1);
            padding: 15px;
            border-bottom: 2px solid rgba(59,130,246,0.3);
            min-height: 40vh;
        }

        .player-header {
            color: #93c5fd;
        }

        .player-cards {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .player-card {
            width: 90px;
            height: 120px;
            background: linear-gradient(145deg, rgba(59,130,246,0.2), rgba(59,130,246,0.1));
            border: 1px solid rgba(59,130,246,0.5);
            border-radius: 10px;
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .player-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 6px 16px rgba(59,130,246,0.3);
        }

        .player-card.ready {
            border-color: #10b981;
            box-shadow: 0 0 15px rgba(16,185,129,0.4);
            animation: pulse 1.5s infinite;
        }

        .card-level {
            position: absolute;
            top: -4px;
            left: -4px;
            background: #f59e0b;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }

        .card-name {
            font-size: 11px;
            font-weight: bold;
            text-align: center;
            margin: 4px 0;
        }

        .card-bottom {
            display: flex;
            justify-content: space-between;
            width: 100%;
            font-size: 11px;
            font-weight: bold;
        }

        /* 底部控制欄 */
        .battle-controls {
            background: rgba(0,0,0,0.8);
            backdrop-filter: blur(20px);
            padding: 15px 20px;
            display: flex;
            justify-content: space-around;
            gap: 10px;
        }

        .battle-btn {
            flex: 1;
            padding: 12px 8px;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 8px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .battle-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
        }

        .battle-btn.speed {
            background: rgba(16,185,129,0.3);
            border-color: rgba(16,185,129,0.5);
        }

        .battle-btn.pause {
            background: rgba(239,68,68,0.3);
            border-color: rgba(239,68,68,0.5);
        }

        /* 動畫效果 */
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes damage {
            0% { transform: scale(1); }
            50% { transform: scale(0.95); background: rgba(239,68,68,0.5); }
            100% { transform: scale(1); }
        }

        .damage-animation {
            animation: damage 0.5s ease;
        }

        /* 響應式調整 */
        @media (max-width: 360px) {
            .battle-container {
                max-width: 100%;
            }
            
            .battle-card, .player-card {
                width: 70px;
                height: 90px;
            }
            
            .card-icon {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="battle-container">
        <!-- 頂部控制欄 -->
        <div class="battle-header">
            <div class="stage-info">⚔️ Stage 1-5</div>
            <div class="header-controls">
                <button class="control-btn" title="暫停">⏸️</button>
                <button class="control-btn" title="音效">🔊</button>
                <button class="control-btn" title="設定">⚙️</button>
            </div>
        </div>

        <!-- 敵方區域 -->
        <div class="enemy-section">
            <div class="section-header enemy-header">
                <span>🔴 ENEMY (3/3)</span>
                <span>❤️ 450</span>
            </div>
            <div class="cards-grid">
                <div class="battle-card enemy">
                    <div class="card-icon">🐺</div>
                    <div class="card-stats">
                        <span>⚔️65</span>
                        <span>⚡12</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 80%;"></div>
                    </div>
                </div>
                <div class="battle-card enemy">
                    <div class="card-icon">🧌</div>
                    <div class="card-stats">
                        <span>⚔️85</span>
                        <span>⚡8</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 60%;"></div>
                    </div>
                </div>
                <div class="battle-card enemy">
                    <div class="card-icon">🕷️</div>
                    <div class="card-stats">
                        <span>⚔️45</span>
                        <span>⚡18</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill" style="width: 90%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中間行動區域 -->
        <div class="action-section">
            <div class="next-action">⚡ 下一個: 火龍戰士 (2秒)</div>
            <div class="action-queue">
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
                <div class="action-indicator enemy">敵</div>
                <div class="action-indicator player">你</div>
            </div>
        </div>

        <!-- 玩家區域 -->
        <div class="player-section">
            <div class="section-header player-header">
                <span>🔵 YOUR TEAM (3/3)</span>
                <span>❤️ 850</span>
            </div>
            <div class="player-cards">
                <div class="player-card ready">
                    <div class="card-level">5</div>
                    <div class="card-icon">🔥</div>
                    <div class="card-name">火龍戰士</div>
                    <div class="card-bottom">
                        <span>⚔️120</span>
                        <span>⚡15</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 100%;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div class="card-level">3</div>
                    <div class="card-icon">🏹</div>
                    <div class="card-name">月光射手</div>
                    <div class="card-bottom">
                        <span>⚔️85</span>
                        <span>⚡18</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 75%;"></div>
                    </div>
                </div>
                <div class="player-card">
                    <div class="card-level">4</div>
                    <div class="card-icon">🛡️</div>
                    <div class="card-name">聖騎士</div>
                    <div class="card-bottom">
                        <span>⚔️75</span>
                        <span>⚡12</span>
                    </div>
                    <div class="hp-bar">
                        <div class="hp-fill player" style="width: 85%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部控制欄 -->
        <div class="battle-controls">
            <button class="battle-btn">📊 詳情</button>
            <button class="battle-btn speed">⏩ 2x</button>
            <button class="battle-btn pause">⏸️ 暫停</button>
            <button class="battle-btn">📷 截圖</button>
        </div>
    </div>

    <script>
        // 戰鬥控制交互
        let battleSpeed = 1;
        let isPaused = false;

        // 速度控制
        document.querySelector('.battle-btn.speed').addEventListener('click', function() {
            battleSpeed = battleSpeed >= 4 ? 1 : battleSpeed * 2;
            this.textContent = `⏩ ${battleSpeed}x`;
        });

        // 暫停控制
        document.querySelector('.battle-btn.pause').addEventListener('click', function() {
            isPaused = !isPaused;
            this.textContent = isPaused ? '▶️ 繼續' : '⏸️ 暫停';
            this.style.background = isPaused ? 'rgba(16,185,129,0.3)' : 'rgba(239,68,68,0.3)';
        });

        // 模擬戰鬥動畫
        function simulateBattle() {
            const cards = document.querySelectorAll('.battle-card, .player-card');
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            
            // 添加傷害動畫
            randomCard.classList.add('damage-animation');
            
            // 隨機減少血量
            const hpBar = randomCard.querySelector('.hp-fill');
            if (hpBar) {
                const currentWidth = parseInt(hpBar.style.width) || 100;
                const newWidth = Math.max(0, currentWidth - Math.random() * 20);
                hpBar.style.width = newWidth + '%';
            }
            
            setTimeout(() => {
                randomCard.classList.remove('damage-animation');
            }, 500);
        }

        // 每3秒模擬一次戰鬥動作
        setInterval(() => {
            if (!isPaused) {
                simulateBattle();
            }
        }, 3000);

        // 卡牌點擊效果
        document.querySelectorAll('.battle-card, .player-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>