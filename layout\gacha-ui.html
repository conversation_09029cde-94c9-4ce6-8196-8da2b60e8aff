<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gacha UI Layout - PetingGame</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
            min-height: 100vh;
            color: white;
            overflow-x: hidden;
        }

        /* 手機直向抽卡佈局 */
        .gacha-container {
            max-width: 375px;
            margin: 0 auto;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 頂部標題欄 */
        .gacha-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .close-btn {
            width: 36px;
            height: 36px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        /* 資源顯示 */
        .resources {
            display: flex;
            justify-content: center;
            gap: 20px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            margin: 0 20px;
            border-radius: 12px;
            backdrop-filter: blur(5px);
        }

        .resource-item {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 16px;
            font-weight: bold;
        }

        /* 主要內容區域 */
        .gacha-content {
            flex: 1;
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* 主要抽卡選項 */
        .primary-gacha {
            background: linear-gradient(135deg, rgba(16,185,129,0.9), rgba(5,150,105,0.9));
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(16,185,129,0.3);
            border: 2px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .gacha-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .gacha-cost {
            font-size: 24px;
            font-weight: bold;
            margin: 12px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .primary-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .primary-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255,255,255,0.2);
        }

        .primary-btn:active {
            transform: translateY(0);
        }

        /* 次要抽卡選項 */
        .secondary-gachas {
            display: flex;
            gap: 12px;
            justify-content: space-between;
        }

        .secondary-gacha {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px 12px;
            text-align: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .secondary-gacha:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .secondary-gacha.premium {
            background: linear-gradient(135deg, rgba(139,92,246,0.3), rgba(124,58,237,0.3));
            border-color: rgba(139,92,246,0.5);
        }

        .secondary-gacha.gold {
            background: linear-gradient(135deg, rgba(245,158,11,0.3), rgba(217,119,6,0.3));
            border-color: rgba(245,158,11,0.5);
        }

        .secondary-gacha.free {
            background: linear-gradient(135deg, rgba(34,197,94,0.3), rgba(22,163,74,0.3));
            border-color: rgba(34,197,94,0.5);
        }

        .secondary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .secondary-cost {
            font-size: 16px;
            font-weight: bold;
            margin: 8px 0;
        }

        .secondary-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
            width: 100%;
        }

        .secondary-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .timer {
            font-size: 12px;
            color: rgba(255,255,255,0.8);
            margin-top: 4px;
        }

        /* 機率說明 */
        .probability-info {
            text-align: center;
            margin-top: 20px;
        }

        .probability-btn {
            background: rgba(255,255,255,0.1);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .probability-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        /* 抽卡結果彈窗 */
        .result-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .result-content {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            max-width: 300px;
            width: 90%;
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 20px 50px rgba(0,0,0,0.3);
        }

        .result-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .result-card {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .result-card-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .result-card-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .result-card-rarity {
            font-size: 16px;
            color: #fbbf24;
            margin-bottom: 12px;
        }

        .result-card-stats {
            display: flex;
            justify-content: space-around;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .result-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .result-btn {
            flex: 1;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .result-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        /* 動畫效果 */
        @keyframes sparkle {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        .sparkle {
            animation: sparkle 2s infinite;
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .slide-up {
            animation: slideUp 0.5s ease forwards;
        }

        /* 響應式調整 */
        @media (max-width: 360px) {
            .gacha-container {
                max-width: 100%;
            }
            
            .gacha-content {
                padding: 20px 15px;
            }
            
            .secondary-gacha {
                padding: 15px 8px;
            }
            
            .secondary-title {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="gacha-container">
        <!-- 頂部標題欄 -->
        <div class="gacha-header">
            <div class="header-title">
                <span>🎴</span>
                <span>卡牌召喚</span>
            </div>
            <button class="close-btn">✕</button>
        </div>

        <!-- 資源顯示 -->
        <div class="resources">
            <div class="resource-item">
                <span>💎</span>
                <span>1,250</span>
            </div>
            <div class="resource-item">
                <span>💰</span>
                <span>5,600</span>
            </div>
        </div>

        <!-- 主要內容 -->
        <div class="gacha-content">
            <!-- 主要抽卡選項 -->
            <div class="primary-gacha slide-up">
                <div class="gacha-title">單次召喚</div>
                <div class="gacha-cost">
                    <span>💎</span>
                    <span>100</span>
                </div>
                <button class="primary-btn" onclick="performGacha('single')">
                    ✨ 立即召喚
                </button>
            </div>

            <!-- 次要抽卡選項 -->
            <div class="secondary-gachas">
                <div class="secondary-gacha premium slide-up" style="animation-delay: 0.1s;">
                    <div class="secondary-title">十連抽卡</div>
                    <div class="secondary-cost">💎 900</div>
                    <button class="secondary-btn" onclick="performGacha('ten')">召喚</button>
                    <div class="timer">保底史詩</div>
                </div>
                
                <div class="secondary-gacha gold slide-up" style="animation-delay: 0.2s;">
                    <div class="secondary-title">金幣抽卡</div>
                    <div class="secondary-cost">💰 1,000</div>
                    <button class="secondary-btn" onclick="performGacha('gold')">召喚</button>
                    <div class="timer">較低機率</div>
                </div>
                
                <div class="secondary-gacha free slide-up" style="animation-delay: 0.3s;">
                    <div class="secondary-title">免費抽卡</div>
                    <div class="secondary-cost">⏰ 23:45</div>
                    <button class="secondary-btn" onclick="performGacha('free')">召喚</button>
                    <div class="timer">每日一次</div>
                </div>
            </div>

            <!-- 機率說明 */
            <div class="probability-info">
                <button class="probability-btn" onclick="showProbability()">
                    📊 抽卡機率說明
                </button>
            </div>
        </div>
    </div>

    <!-- 抽卡結果彈窗 -->
    <div class="result-modal" id="resultModal">
        <div class="result-content">
            <div class="result-title">🎉 召喚結果</div>
            <div class="sparkle">✨ 恭喜獲得新角色！ ✨</div>
            
            <div class="result-card">
                <div class="result-card-icon">🔥</div>
                <div class="result-card-name">火龍戰士</div>
                <div class="result-card-rarity">⭐⭐⭐⭐ (傳說)</div>
                <div class="result-card-stats">
                    <span>攻擊: 130</span>
                    <span>生命: 150</span>
                    <span>速度: 15</span>
                </div>
                <div>🔥 火焰吐息 - 對敵方全體造成火焰傷害</div>
            </div>

            <div class="result-actions">
                <button class="result-btn" onclick="addToTeam()">加入隊伍</button>
                <button class="result-btn" onclick="viewDetails()">查看詳情</button>
            </div>
            
            <button class="result-btn" onclick="continueGacha()" style="margin-top: 10px; background: rgba(16,185,129,0.3);">
                繼續召喚
            </button>
        </div>
    </div>

    <script>
        // 抽卡功能
        function performGacha(type) {
            console.log(`執行 ${type} 抽卡`);
            
            // 模擬抽卡動畫
            const btn = event.target;
            const originalText = btn.textContent;
            
            btn.textContent = '召喚中...';
            btn.style.background = 'rgba(255,255,255,0.1)';
            btn.disabled = true;

            setTimeout(() => {
                showResult();
                btn.textContent = originalText;
                btn.style.background = '';
                btn.disabled = false;
            }, 2000);
        }

        // 顯示抽卡結果
        function showResult() {
            document.getElementById('resultModal').style.display = 'flex';
            
            // 隨機生成結果
            const cards = [
                { icon: '🔥', name: '火龍戰士', rarity: '⭐⭐⭐⭐⭐', attack: 130, health: 150, speed: 15 },
                { icon: '🏹', name: '月光射手', rarity: '⭐⭐⭐⭐', attack: 90, health: 65, speed: 18 },
                { icon: '🛡️', name: '聖騎士', rarity: '⭐⭐⭐', attack: 75, health: 85, speed: 12 },
                { icon: '🐺', name: '森林狼', rarity: '⭐⭐', attack: 45, health: 60, speed: 14 },
                { icon: '👹', name: '暗影惡魔', rarity: '⭐⭐⭐⭐', attack: 95, health: 80, speed: 16 }
            ];
            
            const randomCard = cards[Math.floor(Math.random() * cards.length)];
            
            document.querySelector('.result-card-icon').textContent = randomCard.icon;
            document.querySelector('.result-card-name').textContent = randomCard.name;
            document.querySelector('.result-card-rarity').textContent = randomCard.rarity;
            document.querySelector('.result-card-stats').innerHTML = `
                <span>攻擊: ${randomCard.attack}</span>
                <span>生命: ${randomCard.health}</span>
                <span>速度: ${randomCard.speed}</span>
            `;
        }

        // 加入隊伍
        function addToTeam() {
            alert('卡牌已加入隊伍！');
            closeResult();
        }

        // 查看詳情
        function viewDetails() {
            alert('查看卡牌詳情');
        }

        // 繼續抽卡
        function continueGacha() {
            closeResult();
        }

        // 關閉結果彈窗
        function closeResult() {
            document.getElementById('resultModal').style.display = 'none';
        }

        // 顯示機率說明
        function showProbability() {
            alert(`抽卡機率說明：
            
⭐ 普通: 60%
⭐⭐ 稀有: 25%  
⭐⭐⭐ 史詩: 12%
⭐⭐⭐⭐ 傳說: 2.5%
⭐⭐⭐⭐⭐ 神話: 0.5%

保底機制：
• 十連抽保證至少一張稀有
• 50抽保底一張史詩
• 200抽保底一張傳說`);
        }

        // 關閉按鈕
        document.querySelector('.close-btn').addEventListener('click', function() {
            console.log('關閉抽卡界面');
        });

        // 點擊彈窗外部關閉
        document.getElementById('resultModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeResult();
            }
        });

        // 模擬免費抽卡倒計時
        function updateFreeTimer() {
            const timer = document.querySelector('.free .timer');
            let hours = 23;
            let minutes = 45;
            
            setInterval(() => {
                minutes--;
                if (minutes < 0) {
                    minutes = 59;
                    hours--;
                }
                if (hours < 0) {
                    hours = 23;
                    minutes = 59;
                }
                timer.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
            }, 60000);
        }

        // 初始化
        updateFreeTimer();
    </script>
</body>
</html>